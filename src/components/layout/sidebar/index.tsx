import { SidebarInset, SidebarProvider } from '@mayo/mayo-ui-beta/v2';
import { clsx } from 'clsx';

import { cn } from '@/utils/style';

import { AppSidebar } from './app-sidebar';
import { SiteHeader } from './site-header';

// Constants
const LAYOUT_CONSTANTS = {
  FOOTER_HEIGHT: '48px',
  Z_INDEX: 9999,
} as const;

const IFRAME_MODE_PARAMS = ['isIframeMode', 'isiframemode', 'isIframemode'] as const;

function Sidebar({ children }: { children: React.ReactNode }) {
  // const renderFooter = () => {
  //   return (
  //     <footer className="fixed right-0 bottom-0 z-20 flex h-12 w-full items-center justify-end bg-[rgb(240_240_241)] px-4 py-4">
  //       <p className="text-end text-sm break-words text-gray-500">
  //         Copyright © 2025 MAYO Human Capital Inc. All rights reserved. / 隱私權政策 / About Us
  //       </p>
  //     </footer>
  //   );
  // };
  const queryString = window.location.search;
  const searchParams = new URLSearchParams(queryString);
  const isIframeMode = IFRAME_MODE_PARAMS.some(param => searchParams.get(param) === 'true');
  return (
    <SidebarProvider>
      {!isIframeMode && (
        //  Fixed Header
        <div className={`fixed left-0 top-0 z-[${LAYOUT_CONSTANTS.Z_INDEX}] w-full`}>
          <SiteHeader />
        </div>
      )}

      {/* Main Content Area */}
      <div className={clsx(`mt-[var(--header-height)] flex flex-1`, { 'mt-0': isIframeMode })}>
        <AppSidebar isIframeMode={isIframeMode} />
        <SidebarInset className="h-dvh bg-[rgb(240_240_241)] px-8 py-6">
          <div className={cn('h-full w-full', `mb-[${LAYOUT_CONSTANTS.FOOTER_HEIGHT}]`)}>
            {children}
          </div>
          {/* {renderFooter()} */}
        </SidebarInset>
      </div>
    </SidebarProvider>
  );
}

export default Sidebar;

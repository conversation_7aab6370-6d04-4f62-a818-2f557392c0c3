import { Sidebar, SidebarContent } from '@mayo/mayo-ui-beta/v2';
import { Building2, CalendarCog } from 'lucide-react';
import { useMemo } from 'react';

import { isDev } from '@/utils/config';

import NavMain from './nav-main';

export function AppSidebar({ ...props }: React.ComponentProps<typeof Sidebar>) {
  const data = useMemo(
    () => ({
      demoMain: isDev
        ? [
            {
              title: '測試頁面',
              url: '',
              icon: Building2,
              isActive: true,
              items: [
                {
                  title: 'MSW API 演示',
                  url: '/msw-demo',
                },
                {
                  title: 'Basic Demo',
                  url: '/basic-demo',
                },
                {
                  title: '示範表單',
                  url: '/demo-form',
                },
              ],
            },
          ]
        : [],
      shiftSchedule: [
        {
          title: '班表管理',
          url: '',
          icon: CalendarCog,
          isActive: true,
          items: [
            {
              title: '排班系統',
              url: '/shift-scheduler',
            },
            {
              title: 'timeline-demo',
              url: '/timeline-demo',
            },
            {
              title: '班表審核',
              url: '/shift-schedule-approval',
            },
          ],
        },
      ],
    }),
    [],
  );
  return (
    <Sidebar
      collapsible="icon"
      className="fixed top-[--header-height] !z-30 !h-[calc(100svh-var(--header-height))]"
      {...props}
    >
      <SidebarContent>
        <div>
          <NavMain groupTitle="展示頁" items={data.demoMain} />
          <NavMain groupTitle="管理專區" items={data.shiftSchedule} />
          {/* {data.sections.map((section, index) => (
            <div key={index}>
              <h2 className="px-4 text-lg font-semibold text-gray-200">{section.title}</h2>
              <NavMain items={section.items} />
            </div>
          ))} */}
        </div>
      </SidebarContent>
    </Sidebar>
  );
}

import { Button, CompoundDropdownMenu, CompoundDropdownMenuItem } from '@mayo/mayo-ui-beta/v2';
import { useAtom, useAtomValue } from 'jotai';
import { ChevronDown } from 'lucide-react';
import { useCallback, useMemo } from 'react';
import { useTranslation } from 'react-i18next';

import { useResponsiveContainer } from '@/hooks/useResponsiveContainer';
import { toggleViewModeAtom, viewModeAtom, ViewModeEnum } from '@/lib/jotai/shiftScheduleApproval';

import GenerateScheduleDropdownMenu from './GenerateScheduleDropdownMenu';
import MoreButton from './MoreButton';

// Types
export type ScheduleButtonConfig = CompoundDropdownMenuItem & {
  color?: 'primary' | 'success' | 'error' | 'secondary' | 'warning';
  variant?: 'outline' | 'link' | 'default' | 'ghost';
  render?: (button: ScheduleButtonConfig) => React.ReactNode;
};

// Constants
export const BUTTON_IDS = {
  VIEW: 'view', // 檢視
  GENERATE: 'generate', // 產生班表
  SUPPORT: 'support', // 支援排班
  CHECK: 'check', // 檢查
  EDIT: 'edit', // 編輯
  PUBLISH: 'publish', // 發布
} as const;

const LAYOUT_CONFIG = {
  MORE_BUTTON_WIDTH: 36,
  BUTTON_GAP: 8,
  MIN_VISIBLE_BUTTONS: 1,
} as const;

// Utils
const filterButtonsByViewMode = (
  allButtons: ScheduleButtonConfig[],
  isViewMode: boolean,
): ScheduleButtonConfig[] => {
  const viewModeButtons: (typeof BUTTON_IDS)[keyof typeof BUTTON_IDS][] = [
    BUTTON_IDS.GENERATE,
    BUTTON_IDS.SUPPORT,
    BUTTON_IDS.CHECK,
    BUTTON_IDS.EDIT,
  ];
  const editModeButtons: (typeof BUTTON_IDS)[keyof typeof BUTTON_IDS][] = [
    BUTTON_IDS.VIEW,
    BUTTON_IDS.GENERATE,
    BUTTON_IDS.SUPPORT,
    BUTTON_IDS.CHECK,
    BUTTON_IDS.PUBLISH,
  ];

  const allowedIds = isViewMode ? viewModeButtons : editModeButtons;
  return allButtons.filter(button => allowedIds.includes(button.id as (typeof allowedIds)[number]));
};

const ScheduleButtonGroup: React.FC = () => {
  const { t } = useTranslation();
  const [, toggleViewMode] = useAtom(toggleViewModeAtom);
  const viewMode = useAtomValue(viewModeAtom);

  // Computed Values
  const isViewMode = viewMode === ViewModeEnum.VIEW;

  // Button configurations
  const createButtonConfigs = useCallback(
    (): ScheduleButtonConfig[] => [
      {
        id: BUTTON_IDS.VIEW,
        type: 'item',
        label: t('檢視'),
        onClick: toggleViewMode,
        variant: 'outline',
      },
      {
        id: BUTTON_IDS.GENERATE,
        type: 'sub',
        label: t('產生班表'),
        subItems: [
          {
            id: 'generate-default',
            type: 'item',
            label: t('產生預設班表'),
            onClick: () => alert('產生班表'),
          },
          {
            id: 'generate-import',
            type: 'item',
            label: t('班表匯入'),
            onClick: () => alert('班表匯入'),
          },
        ],
      },
      {
        id: BUTTON_IDS.SUPPORT,
        type: 'item',
        label: t('支援排班'),
        onClick: () => alert('支援排班'),
      },
      {
        id: BUTTON_IDS.CHECK,
        type: 'item',
        label: t('檢查'),
        onClick: () => alert('檢查'),
      },
      {
        id: BUTTON_IDS.EDIT,
        type: 'item',
        label: t('編輯'),
        onClick: toggleViewMode,
      },
      {
        id: BUTTON_IDS.PUBLISH,
        type: 'item',
        label: t('發布'),
        onClick: () => alert('發布'),
        color: 'success',
      },
    ],
    [t, toggleViewMode],
  );

  // Filter buttons based on view mode
  const buttons = useMemo(() => {
    const allButtons = createButtonConfigs();
    return filterButtonsByViewMode(allButtons, isViewMode);
  }, [isViewMode, createButtonConfigs]);

  // Calculate button items based on Responsive Container
  const {
    containerRef,
    measureRef,
    visibleItems: visibleButtons,
    hiddenItems: hiddenButtons,
    hasHiddenItems: hasHiddenButtons,
    // remeasure,
  } = useResponsiveContainer(buttons, {
    moreButtonWidth: LAYOUT_CONFIG.MORE_BUTTON_WIDTH,
    itemGap: LAYOUT_CONFIG.BUTTON_GAP,
    minVisibleItems: LAYOUT_CONFIG.MIN_VISIBLE_BUTTONS,
    measureSelector: '[data-button-id]',
    measureAttribute: 'data-button-id',
  });

  // Button renderers

  const renderButton = useCallback(
    (button: ScheduleButtonConfig) => {
      if (button.id === BUTTON_IDS.GENERATE && button.type === 'sub') {
        return <GenerateScheduleDropdownMenu key={button.id} buttons={buttons} />;
      }

      if (button.type === 'item') {
        return (
          <Button
            key={button.id}
            variant={button.variant}
            color={button.color}
            onClick={button.onClick}
          >
            {button.label}
          </Button>
        );
      }

      return null;
    },
    [GenerateScheduleDropdownMenu],
  );

  return (
    <div className="ml-4 flex-1 overflow-hidden">
      {/* 隱藏的測量容器 */}
      <div
        ref={measureRef}
        className="pointer-events-none fixed left-0 top-0 z-[-1] flex items-center gap-2 opacity-0"
        style={{ visibility: 'hidden' }}
      >
        {buttons.map(button => (
          <Button key={button.id} data-button-id={button.id}>
            {button.type === 'sub' ? (
              <div className="flex items-center">
                {button.label}
                <ChevronDown size={16} className="ml-2" />
              </div>
            ) : (
              button.label
            )}
          </Button>
        ))}
      </div>

      {/* 響應式按鈕 */}
      <div ref={containerRef}>
        <div className="flex items-center justify-end gap-2">
          {hasHiddenButtons && <MoreButton menuItems={hiddenButtons.reverse()} />}
          {visibleButtons.map(button => renderButton(button))}
        </div>
      </div>
    </div>
  );
};

export default ScheduleButtonGroup;
